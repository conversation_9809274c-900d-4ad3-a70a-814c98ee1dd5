import { Context } from 'koa';
import { BaiduTranslationService } from '../../services/baidu-translation.service';
import { YoudaoTranslationService } from '../../services/youdao-translation.service';
import { AlibabaTranslationService } from '../../services/alibaba-translation.service';
import { TranslationRouteService } from '../../services/translation-route.service';
import { Account } from '../../entities/Account';
import { VendorType } from '../../entities/TranslationRoute';

interface TranslationRequest {
  text: string;
  from: string;
  to: string;
  vendor?: VendorType; // 可选，指定翻译供应商
}

interface TranslateImageRequest {
  imageBase64: string;
  from: string;
  to: string;
  vendor?: VendorType;
}

interface TranslateDocumentRequest {
  documentBase64: string;
  from: string;
  to: string;
  format: string;
  filename: string;
  vendor?: VendorType;
  domain?: string;
  outputFormats?: string[];
  filenamePrefix?: string;
}

interface QueryDocumentTranslationRequest {
  taskId: string;
  vendor?: VendorType;
}

export class TranslationController {
  private baiduTranslationService: BaiduTranslationService;
  private youdaoTranslationService: YoudaoTranslationService;
  private alibabaTranslationService: AlibabaTranslationService;
  private routeService: TranslationRouteService;

  constructor() {
    this.baiduTranslationService = new BaiduTranslationService();
    this.youdaoTranslationService = new YoudaoTranslationService();
    this.alibabaTranslationService = new AlibabaTranslationService();
    this.routeService = new TranslationRouteService();
  }

  /**
   * 执行翻译
   */
  async translate(ctx: Context) {
    try {
      const { text, from, to, vendor } = ctx.request.body as TranslationRequest;
      const { user } = ctx.state;

      if (!text || !from || !to) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '缺少必要参数',
        };
        return;
      }

      let result: string | null = null;

      // 根据指定的供应商或默认使用百度翻译
      if (vendor === VendorType.YOUDAO) {
        result = await this.youdaoTranslationService.smartTranslate(
          text,
          from,
          to,
          (user as Account).id,
        );
      } else {
        result = await this.baiduTranslationService.smartTranslate(
          text,
          from,
          to,
          (user as Account).id,
        );
      }

      if (result === null) {
        ctx.status = 500;
        ctx.body = {
          code: 500,
          message: '翻译失败',
        };
        return;
      }

      ctx.body = {
        code: 200,
        data: {
          text: result,
        },
        message: '翻译成功',
      };
    } catch (error) {
      console.error('翻译失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '翻译失败',
      };
    }
  }

  /**
   * 执行图片翻译
   */
  async translateImage(ctx: Context) {
    try {
      const { imageBase64, from, to, vendor } = ctx.request.body as TranslateImageRequest;
      const { user } = ctx.state;

      if (!imageBase64 || !from || !to) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '缺少必要参数',
        };
        return;
      }

      let result: string | null = null;

      // 支持有道、阿里云和百度图片翻译
      if (vendor === VendorType.YOUDAO) {
        result = await this.youdaoTranslationService.smartTranslateImage(
          imageBase64,
          from,
          to,
          (user as Account).id,
        );
      } else if (vendor === VendorType.ALIBABA) {
        result = await this.alibabaTranslationService.smartTranslateImage(
          imageBase64,
          from,
          to,
          (user as Account).id,
        );
      } else if (vendor === VendorType.BAIDU) {
        result = await this.baiduTranslationService.smartTranslateImage(
          imageBase64,
          from,
          to,
          (user as Account).id,
        );
      } else {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '不支持的图片翻译供应商，目前支持：youdao、alibaba、baidu',
        };
        return;
      }

      if (result === null) {
        ctx.status = 500;
        ctx.body = {
          code: 500,
          message: '图片翻译失败',
        };
        return;
      }

      ctx.body = {
        code: 200,
        data: {
          text: result,
        },
        message: '图片翻译成功',
      };
    } catch (error) {
      console.error('图片翻译失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '图片翻译失败',
      };
    }
  }

  /**
   * 获取可用翻译线路
   */
  async getAvailableRoutes(ctx: Context) {
    try {
      const { user } = ctx.state;
      const accountId = (user as Account).id;

      // 获取所有可用的翻译线路
      const baiduRoutes = await this.baiduTranslationService.getAvailableBaiduRoutes(accountId);
      const youdaoRoutes = await this.youdaoTranslationService.getAvailableYoudaoRoutes(accountId);
      const alibabaImageRoutes =
        await this.alibabaTranslationService.getAvailableAlibabaImageRoutes(accountId);

      ctx.body = {
        code: 200,
        data: {
          baidu: baiduRoutes,
          youdao: youdaoRoutes,
          alibaba: alibabaImageRoutes,
        },
        message: '获取成功',
      };
    } catch (error) {
      console.error('获取翻译线路失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '获取翻译线路失败',
      };
    }
  }

  // 获取支持的语言列表
  async getSupportedLanguages(ctx: Context) {
    try {
      const { vendor, type = 'source' } = ctx.query;

      if (!vendor) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '缺少必要参数：vendor',
        };
        return;
      }

      if (!Object.values(VendorType).includes(vendor as VendorType)) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '不支持的供应商类型',
        };
        return;
      }

      if (type !== 'source' && type !== 'target') {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '参数type必须是source或target',
        };
        return;
      }

      const languages = await this.routeService.getSupportedLanguages(
        vendor as VendorType,
        type as 'source' | 'target',
      );
      if (!languages) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '未找到该供应商支持的语言列表',
        };
        return;
      }

      ctx.body = {
        code: 200,
        data: languages,
        message: '获取成功',
      };
    } catch (error) {
      console.error('获取支持语言列表失败:', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '获取支持语言列表失败',
      };
    }
  }

  /**
   * 创建文档翻译任务
   */
  async translateDocument(ctx: Context) {
    try {
      const {
        documentBase64,
        from,
        to,
        format,
        filename,
        vendor,
        domain,
        outputFormats,
        filenamePrefix,
      } = ctx.request.body as TranslateDocumentRequest;
      const { user } = ctx.state;

      // 验证用户信息
      if (!user || !user.id) {
        ctx.status = 401;
        ctx.body = {
          code: 401,
          message: '用户未认证或用户信息无效',
        };
        return;
      }

      console.log('文档翻译请求用户信息:', {
        userId: user.id,
        userEmail: user.email,
        userName: user.name,
      });

      if (!documentBase64 || !from || !to || !format || !filename) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '缺少必要参数：documentBase64, from, to, format, filename',
        };
        return;
      }

      // 目前只支持百度文档翻译
      if (vendor !== VendorType.BAIDU) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '文档翻译目前只支持百度翻译',
        };
        return;
      }

      const result = await this.baiduTranslationService.smartTranslateDocument(
        documentBase64,
        from,
        to,
        format,
        filename,
        (user as Account).id,
        domain,
        outputFormats,
        filenamePrefix,
      );

      if (!result) {
        ctx.status = 500;
        ctx.body = {
          code: 500,
          message: '文档翻译任务创建失败',
        };
        return;
      }

      ctx.body = {
        code: 200,
        data: {
          taskId: result.taskId,
          dbTaskId: result.dbTaskId,
          message: '文档翻译任务创建成功，请使用taskId查询翻译状态',
        },
        message: '创建成功',
      };
    } catch (error: any) {
      console.error('文档翻译失败:', error);
      const errorMessage = error?.message || '文档翻译失败，请稍后重试';
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: errorMessage,
      };
    }
  }

  /**
   * 查询文档翻译任务状态
   */
  async queryDocumentTranslation(ctx: Context) {
    try {
      const { taskId, vendor } = ctx.request.body as QueryDocumentTranslationRequest;
      const { user } = ctx.state;

      if (!taskId) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '缺少必要参数：taskId',
        };
        return;
      }

      // 目前只支持百度文档翻译
      if (vendor !== VendorType.BAIDU) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '文档翻译目前只支持百度翻译',
        };
        return;
      }

      // 获取用户的百度文档翻译线路
      const routes = await this.baiduTranslationService.getAvailableBaiduDocRoutes(
        (user as Account).id,
      );
      if (routes.length === 0) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '没有可用的百度文档翻译线路',
        };
        return;
      }

      // 使用第一个可用线路查询（同时更新数据库记录）
      const route = routes[0];
      const result = await this.baiduTranslationService.queryDocumentTranslationWithUpdate(
        taskId,
        route,
      );

      if (!result) {
        ctx.status = 500;
        ctx.body = {
          code: 500,
          message: '查询文档翻译状态失败',
        };
        return;
      }

      ctx.body = {
        code: 200,
        data: result.data,
        message: '查询成功',
      };
    } catch (error: any) {
      console.error('查询文档翻译状态失败:', error);
      const errorMessage = error?.message || '查询文档翻译状态失败，请稍后重试';
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: errorMessage,
      };
    }
  }

  /**
   * 获取用户的文档翻译任务列表
   */
  async getDocumentTasks(ctx: Context) {
    try {
      const { user } = ctx.state;
      const { status, limit = 20, offset = 0 } = ctx.query;

      // 验证用户信息
      if (!user || !user.id) {
        ctx.status = 401;
        ctx.body = {
          code: 401,
          message: '用户未认证或用户信息无效',
        };
        return;
      }

      console.log('获取任务列表请求用户信息:', {
        userId: user.id,
        userEmail: user.email,
        userName: user.name,
      });

      const result = await this.baiduTranslationService.getUserDocumentTasks((user as Account).id, {
        status: status as any,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
      });

      ctx.body = {
        code: 200,
        data: {
          tasks: result.tasks,
          total: result.total,
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
        },
        message: '获取成功',
      };
    } catch (error: any) {
      console.error('获取文档翻译任务列表失败:', error);
      const errorMessage = error?.message || '获取文档翻译任务列表失败，请稍后重试';
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: errorMessage,
      };
    }
  }

  /**
   * 获取用户的文档翻译统计信息
   */
  async getDocumentTaskStatistics(ctx: Context) {
    try {
      const { user } = ctx.state;

      const statistics = await this.baiduTranslationService.getUserDocumentTaskStatistics(
        (user as Account).id,
      );

      ctx.body = {
        code: 200,
        data: statistics,
        message: '获取成功',
      };
    } catch (error: any) {
      console.error('获取文档翻译统计信息失败:', error);
      const errorMessage = error?.message || '获取文档翻译统计信息失败，请稍后重试';
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: errorMessage,
      };
    }
  }
}
